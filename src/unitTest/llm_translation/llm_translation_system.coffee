###
LLM翻译系统V2.0测试用例
测试改进后的LLM翻译提示词模板系统的各项功能

测试内容：
1. 上下文标识符提取和处理
2. 多语言翻译支持
3. 智能模板选择
4. 房产领域默认上下文
5. auto_translate_i18n.coffee集成测试

运行方法：
cd /home/<USER>/rm/rmconfig
./unitTest/test.sh -f llm_translation/llm_translation_system.js -e -b

<AUTHOR>
@date 2025-07-02
###

llmHelper = require '../../built/libapp/llmTranslationHelper'

describe 'LLM翻译系统测试', ->
  before (done) ->
    @timeout(30000)
    done()

  describe '上下文标识符提取测试', ->
    it '应该正确提取_id中的上下文标识符', (done) ->
      testCases = [
        {
          input: 'waterloo:prop'
          expected: {cleanText: 'waterloo', context: 'prop', contextDesc: '加拿大房产信息'}
        }
        {
          input: 'toronto:city'
          expected: {cleanText: 'toronto', context: 'city', contextDesc: '城市信息'}
        }
        {
          input: 'elementary school:school'
          expected: {cleanText: 'elementary school', context: 'school', contextDesc: '学校信息'}
        }
        {
          input: 'save changes'
          expected: {cleanText: 'save changes', context: '', contextDesc: ''}
        }
        {
          input: 'menu item:menu'
          expected: {cleanText: 'menu item', context: 'menu', contextDesc: '菜单界面'}
        }
      ]

      for testCase in testCases
        result = llmHelper.extractContextFromId(testCase.input)
        should.equal result.cleanText, testCase.expected.cleanText
        should.equal result.context, testCase.expected.context
        should.equal result.contextDesc, testCase.expected.contextDesc

      done()

  describe '默认上下文生成测试', ->
    it '应该为短文本自动添加房产上下文', (done) ->
      # 单词级翻译应该获得房产上下文
      result1 = llmHelper.getDefaultContext('bedroom', '')
      should.equal result1, '加拿大房产信息'

      # 短语级翻译应该获得房产上下文
      result2 = llmHelper.getDefaultContext('granite', '')
      should.equal result2, '加拿大房产信息'

      # 长文本不应该自动添加房产上下文
      result3 = llmHelper.getDefaultContext('This is a long sentence with multiple words and spaces', '')
      should.equal result3, ''

      # 已有上下文的情况
      result4 = llmHelper.getDefaultContext('bedroom', 'menu')
      should.equal result4, '菜单界面'

      done()

  describe '智能场景选择测试', ->
    it '应该根据文本特征和上下文选择合适的翻译场景', (done) ->
      # 短文本应该选择UI翻译
      result1 = llmHelper.selectTranslationScenario('save', '')
      should.equal result1, 'ui_translation'

      # 房产上下文应该选择房产翻译
      result2 = llmHelper.selectTranslationScenario('bedroom', '加拿大房产信息')
      should.equal result2, 'property_translation'

      # 长文本应该选择论坛翻译
      longText = 'This is a very long text that contains multiple sentences and should be classified as forum content for translation purposes.'
      longText += longText
      result3 = llmHelper.selectTranslationScenario(longText, '')
      should.equal result3, 'forum_translation'

      # 默认应该选择通用翻译
      result4 = llmHelper.selectTranslationScenario('medium length text', '')
      should.equal result4, 'universal_translation'

      done()

  describe '模板变量替换测试', ->
    it '应该正确替换模板中的变量', (done) ->
      template = 'Translate {text} from {source_language} to {target_language}. Context: {context}'
      variables = {
        text: 'hello'
        source_language: 'English'
        target_language: 'Chinese Simplified'
        context: '菜单界面'
      }

      result = llmHelper.replaceTemplateVariables(template, variables)
      expected = 'Translate hello from English to Chinese Simplified. Context: 菜单界面'
      should.equal result, expected

      done()

  describe '模板结构验证测试', ->
    it '应该正确验证模板数据结构', (done) ->
      # 有效模板
      validTemplate = {
        _id: 'test_template'
        nm: '测试模板'
        scenario: 'test'
        tpl: {
          main: 'Test template: {text}'
        }
        vars: [
          {nm: 'text', tp: 'string', req: true}
        ]
      }

      result1 = llmHelper.validateTemplateStructure(validTemplate)
      should.equal result1.valid, true
      should.equal result1.errors.length, 0

      # 无效模板
      invalidTemplate = {
        nm: '缺少ID的模板'
      }

      result2 = llmHelper.validateTemplateStructure(invalidTemplate)
      should.equal result2.valid, false
      should.ok result2.errors.length > 0

      done()

  describe '语言映射测试', ->
    it '应该正确映射语言代码到语言名称', (done) ->
      should.equal llmHelper.LANGUAGE_MAPPING['en'], 'English'
      should.equal llmHelper.LANGUAGE_MAPPING['zh-cn'], 'Chinese Simplified'
      should.equal llmHelper.LANGUAGE_MAPPING['zh'], 'Chinese Traditional'
      should.equal llmHelper.LANGUAGE_MAPPING['kr'], 'Korean'

      should.equal llmHelper.LANGUAGE_NATIVE_NAMES['zh-cn'], '中文简体'
      should.equal llmHelper.LANGUAGE_NATIVE_NAMES['zh'], '中文繁體'
      should.equal llmHelper.LANGUAGE_NATIVE_NAMES['kr'], '한국어'

      done()

  describe '上下文映射测试', ->
    it '应该正确映射上下文标识符到描述', (done) ->
      should.equal llmHelper.CONTEXT_MAPPING['prop'], '加拿大房产信息'
      should.equal llmHelper.CONTEXT_MAPPING['school'], '学校信息'
      should.equal llmHelper.CONTEXT_MAPPING['city'], '城市信息'
      should.equal llmHelper.CONTEXT_MAPPING['menu'], '菜单界面'
      should.equal llmHelper.CONTEXT_MAPPING['forum'], '论坛讨论'

      done()

  describe '翻译报告生成测试', ->
    it '应该生成格式化的翻译报告', (done) ->
      stats = {
        processed: 100
        success: 85
        failed: 10
        skipped: 5
        contextStats: {
          '加拿大房产信息': 50
          '学校信息': 20
          '菜单界面': 15
        }
        languageStats: {
          'zh-cn': 60
          'kr': 25
          'zh': 15
        }
        errorDetails: [
          {id: 'test1', error: '翻译失败'}
          {id: 'test2', error: '网络错误'}
        ]
      }

      report = llmHelper.generateTranslationReport(stats)
      
      should.ok report.includes('总处理记录: 100')
      should.ok report.includes('翻译成功: 85')
      should.ok report.includes('成功率: 85.00%')
      should.ok report.includes('加拿大房产信息: 50 次')
      should.ok report.includes('中文简体: 60 次')
      should.ok report.includes('test1: 翻译失败')

      done()

  describe '错误处理测试', ->
    it '应该优雅处理各种错误情况', (done) ->
      # 测试空值处理
      result1 = llmHelper.extractContextFromId(null)
      should.equal result1.cleanText, null
      should.equal result1.context, ''

      result2 = llmHelper.extractContextFromId('')
      should.equal result2.cleanText, ''
      should.equal result2.context, ''

      # 测试无效模板验证
      result3 = llmHelper.validateTemplateStructure(null)
      should.equal result3.valid, false
      should.ok result3.errors.length > 0

      # 测试变量替换错误处理
      result4 = llmHelper.replaceTemplateVariables(null, {})
      should.equal result4, null

      done()

  describe '集成测试', ->
    it '应该能够完整处理带上下文标识符的翻译流程', (done) ->
      # 模拟完整的翻译流程
      originalId = 'waterloo:prop'

      # 1. 提取上下文
      contextInfo = llmHelper.extractContextFromId(originalId)
      should.equal contextInfo.cleanText, 'waterloo'
      should.equal contextInfo.contextDesc, '加拿大房产信息'

      # 2. 获取默认上下文
      defaultContext = llmHelper.getDefaultContext(contextInfo.cleanText, contextInfo.context)
      should.equal defaultContext, '加拿大房产信息'

      # 3. 选择翻译场景
      scenario = llmHelper.selectTranslationScenario(contextInfo.cleanText, defaultContext)
      should.equal scenario, 'property_translation'

      # 4. 验证最终结果
      should.equal contextInfo.cleanText, 'waterloo'  # 清理后的文本用于翻译
      should.equal defaultContext, '加拿大房产信息'  # 上下文用于提示词
      should.equal scenario, 'property_translation'  # 选择的场景

      done()

  describe 'LLM翻译模型修复功能测试', ->

    describe 'translatorList参数传递测试', ->
      it '应该从模板配置中正确提取translatorList', (done) ->
        # 模拟auto_translate_i18n.coffee中的getTranslatorListFromTemplate函数
        getTranslatorListFromTemplate = (template) ->
          unless template?.m_cfg?.m_nm
            return ['openAI', 'claude', 'gemini', 'deepseek']

          modelName = template.m_cfg.m_nm

          # 将模型名称映射到translatorManager的key
          modelMapping = {
            'gpt': 'openAI'
            'openai': 'openAI'
            'claude': 'claude'
            'gemini': 'gemini'
            'deepseek': 'deepseek'
            'grok': 'grok'
            'rm': 'rm'
          }

          translatorKey = modelMapping[modelName.toLowerCase()]

          if translatorKey
            # 返回指定模型优先的列表
            priorityList = [translatorKey]
            # 添加其他模型作为fallback
            AI_MODEL_PRIORITY = ['openAI', 'claude', 'gemini', 'deepseek', 'grok', 'rm']
            for model in AI_MODEL_PRIORITY
              if model isnt translatorKey
                priorityList.push model
            return priorityList
          else
            return ['openAI', 'claude', 'gemini', 'deepseek']

        # 测试GPT模型
        gptTemplate = {
          m_cfg: { m_nm: 'gpt' }
        }
        result1 = getTranslatorListFromTemplate(gptTemplate)
        should.equal result1[0], 'openAI'
        should.ok result1.length > 1

        # 测试Claude模型
        claudeTemplate = {
          m_cfg: { m_nm: 'claude' }
        }
        result2 = getTranslatorListFromTemplate(claudeTemplate)
        should.equal result2[0], 'claude'
        should.ok result2.includes('openAI')

        # 测试缺少配置的情况
        emptyTemplate = {}
        result3 = getTranslatorListFromTemplate(emptyTemplate)
        should.ok Array.isArray(result3)
        should.ok result3.length > 0

        done()

      it '应该验证translateWithPrompt调用包含translatorList参数', (done) ->
        # 模拟translateWithPrompt调用验证
        mockTranslateWithPrompt = (content, prompt, targetLang, options) ->
          # 验证必要参数
          should.ok content, 'content参数不能为空'
          should.ok prompt, 'prompt参数不能为空'
          should.ok targetLang, 'targetLang参数不能为空'
          should.ok options, 'options参数不能为空'
          should.ok options.translatorList, 'options.translatorList参数不能为空'
          should.ok Array.isArray(options.translatorList), 'translatorList必须是数组'
          should.ok options.translatorList.length > 0, 'translatorList不能为空数组'

          return {
            success: true
            result: "翻译结果: #{content}"
            service: options.translatorList[0]
          }

        # 模拟正确的调用
        result = mockTranslateWithPrompt(
          'test content',
          'test prompt',
          'zh-cn',
          {
            skipI18nFilter: true
            translatorList: ['openAI', 'claude', 'gemini']
          }
        )

        should.equal result.success, true
        should.equal result.service, 'openAI'

        done()

    describe '多AI模型支持测试', ->
      it '应该支持所有主要AI模型', (done) ->
        # 支持的AI模型列表
        supportedModels = ['gpt', 'claude', 'gemini', 'deepseek', 'grok', 'rm']

        # 模型到translatorManager key的映射
        modelMapping = {
          'gpt': 'openAI'
          'claude': 'claude'
          'gemini': 'gemini'
          'deepseek': 'deepseek'
          'grok': 'grok'
          'rm': 'rm'
        }

        # 验证每个模型都有对应的映射
        for model in supportedModels
          should.ok modelMapping[model], "模型 #{model} 应该有对应的映射"

        # 验证映射的唯一性
        mappedValues = Object.values(modelMapping)
        uniqueValues = [...new Set(mappedValues)]
        should.equal mappedValues.length, uniqueValues.length, '映射值应该是唯一的'

        done()

      it '应该正确生成多AI模型的模板变体', (done) ->
        # 模拟generateModelVariant函数
        generateModelVariant = (baseTemplate, modelName) ->
          modelDisplayNames = {
            'gpt': 'GPT'
            'claude': 'Claude'
            'gemini': 'Gemini'
            'deepseek': 'DeepSeek'
            'grok': 'Grok'
          }

          # 创建新模板
          newTemplate = JSON.parse(JSON.stringify(baseTemplate))

          # 更新ID和名称
          originalId = baseTemplate._id
          newTemplate._id = originalId.replace(/_gpt_/, "_#{modelName}_")
          newTemplate.nm = baseTemplate.nm.replace('(GPT)', "(#{modelDisplayNames[modelName] or modelName})")
          newTemplate.desc = baseTemplate.desc.replace('使用GPT模型', "使用#{modelDisplayNames[modelName] or modelName}模型")

          # 更新模型配置
          newTemplate.m_cfg.m_nm = modelName

          return newTemplate

        # 基础GPT模板
        baseTemplate = {
          _id: 'test_translation_gpt_v1'
          nm: '测试翻译模板 (GPT)'
          desc: '测试用模板，使用GPT模型'
          m_cfg: { m_nm: 'gpt' }
          tpl: { main: 'Test prompt' }
        }

        # 生成Claude版本
        claudeTemplate = generateModelVariant(baseTemplate, 'claude')
        should.equal claudeTemplate._id, 'test_translation_claude_v1'
        should.ok claudeTemplate.nm.includes('Claude')
        should.equal claudeTemplate.m_cfg.m_nm, 'claude'

        # 生成Gemini版本
        geminiTemplate = generateModelVariant(baseTemplate, 'gemini')
        should.equal geminiTemplate._id, 'test_translation_gemini_v1'
        should.ok geminiTemplate.nm.includes('Gemini')
        should.equal geminiTemplate.m_cfg.m_nm, 'gemini'

        done()

    describe 'prompt模版获取策略测试', ->
      it '应该按优先级选择最佳模板', (done) ->
        # 模拟selectBestTemplate函数
        selectBestTemplate = (templates) ->
          unless templates?.length > 0
            return null

          # 如果只有一个模板，直接返回
          if templates.length is 1
            return templates[0]

          # 按照AI模型优先级选择模板
          AI_MODEL_PRIORITY = ['openAI', 'claude', 'gemini', 'deepseek', 'grok', 'rm']

          for modelKey in AI_MODEL_PRIORITY
            modelMapping = {
              'openAI': ['gpt', 'openai']
              'claude': ['claude']
              'gemini': ['gemini']
              'deepseek': ['deepseek']
              'grok': ['grok']
              'rm': ['rm']
            }

            modelNames = modelMapping[modelKey] or []

            for template in templates
              if template.m_cfg?.m_nm
                templateModel = template.m_cfg.m_nm.toLowerCase()
                if templateModel in modelNames
                  return template

          # 如果没有匹配的模型，返回第一个模板
          return templates[0]

        # 测试单个模板
        singleTemplate = [{
          _id: 'test_template_1'
          m_cfg: { m_nm: 'gpt' }
        }]
        result1 = selectBestTemplate(singleTemplate)
        should.equal result1._id, 'test_template_1'

        # 测试多个模板，按优先级选择
        multipleTemplates = [
          { _id: 'deepseek_template', m_cfg: { m_nm: 'deepseek' } }
          { _id: 'openai_template', m_cfg: { m_nm: 'gpt' } }
          { _id: 'claude_template', m_cfg: { m_nm: 'claude' } }
        ]
        result2 = selectBestTemplate(multipleTemplates)
        should.equal result2._id, 'openai_template'  # openAI优先级最高

        done()

      it '应该实现完善的fallback机制', (done) ->
        # 模拟智能模板获取策略
        getSmartTranslationPrompt = (text, sourceLang, targetLang, context) ->
          # 模拟查询结果
          mockTemplates = [
            {
              _id: 'ui_translation_claude_v1'
              scenario: 'ui_translation'
              status: 'active'
              tags: ['ui', 'multilingual']
              m_cfg: { m_nm: 'claude' }
              tpl: { main: 'UI translation prompt' }
            }
            {
              _id: 'universal_translation_gpt_v1'
              scenario: 'universal_translation'
              status: 'active'
              tags: ['universal', 'multilingual']
              m_cfg: { m_nm: 'gpt' }
              tpl: { main: 'Universal translation prompt' }
            }
          ]

          # 模拟场景选择
          scenario = if text.length < 10 then 'ui_translation' else 'universal_translation'

          # 查找匹配的模板
          matchingTemplates = mockTemplates.filter (t) -> t.scenario is scenario

          # 如果没有找到，使用fallback
          if matchingTemplates.length is 0
            matchingTemplates = mockTemplates.filter (t) -> t.scenario is 'universal_translation'

          # 如果还是没有，使用任何可用模板
          if matchingTemplates.length is 0
            matchingTemplates = mockTemplates

          if matchingTemplates.length is 0
            return {
              success: false
              error: '未找到任何可用的翻译模板'
            }

          # 选择最佳模板
          selectedTemplate = matchingTemplates[0]  # 简化版本

          return {
            success: true
            prompt: selectedTemplate.tpl.main
            template: selectedTemplate._id
            scenario: scenario
            translatorList: ['openAI', 'claude', 'gemini']
          }

        # 测试短文本（UI场景）
        result1 = getSmartTranslationPrompt('Save', 'en', 'zh-cn', '')
        should.equal result1.success, true
        should.equal result1.scenario, 'ui_translation'
        should.ok result1.translatorList

        # 测试长文本（通用场景）
        result2 = getSmartTranslationPrompt('This is a longer text', 'en', 'zh-cn', '')
        should.equal result2.success, true
        should.equal result2.scenario, 'universal_translation'
        should.ok result2.translatorList

        done()

    describe '修复功能集成测试', ->
      it '应该完整验证修复后的翻译流程', (done) ->
        # 模拟完整的修复后翻译流程

        # 1. 模拟模板查询和选择
        mockTemplate = {
          _id: 'ui_translation_gpt_v1'
          scenario: 'ui_translation'
          m_cfg: { m_nm: 'gpt' }
          tpl: { main: 'Translate {text} from {source_language} to {target_language}' }
        }

        # 2. 提取translatorList
        getTranslatorListFromTemplate = (template) ->
          if template.m_cfg.m_nm is 'gpt'
            return ['openAI', 'claude', 'gemini', 'deepseek']
          else
            return ['claude', 'openAI', 'gemini', 'deepseek']

        translatorList = getTranslatorListFromTemplate(mockTemplate)
        should.ok Array.isArray(translatorList)
        should.equal translatorList[0], 'openAI'

        # 3. 模拟translateWithPrompt调用
        mockTranslateWithPrompt = (content, prompt, targetLang, options) ->
          should.ok options.translatorList, 'translatorList参数必须存在'
          should.equal options.translatorList[0], 'openAI', '应该使用正确的模型优先级'

          return {
            success: true
            result: "翻译结果: #{content}"
            service: options.translatorList[0]
          }

        # 4. 执行完整调用
        result = mockTranslateWithPrompt(
          'Save',
          'Translate Save from English to Chinese Simplified',
          'zh-cn',
          {
            skipI18nFilter: true
            translatorList: translatorList
          }
        )

        should.equal result.success, true
        should.equal result.service, 'openAI'
        should.ok result.result.includes('Save')

        done()
