###
LLM翻译提示词模板导入脚本
包含模板数据定义和导入逻辑的独立批处理文件

Usage:          ./start.sh -n import_llm_templates -cmd "lib/batchBase.coffee batch/prompts/import_llm_templates.coffee --category all"
                ./start.sh -n import_llm_templates -cmd "lib/batchBase.coffee batch/prompts/import_llm_templates.coffee --category ui --force"

Parameters:
  --category <name>     指定导入类别 (universal, ui, forum, property, comment, all)
  --force               强制更新现有模板
  --dryrun              干运行模式

Features:
  - 支持多语言翻译（任意语言对）
  - 上下文感知翻译
  - 房产领域智能上下文
  - 批量导入和更新管理

Create date:    2025-07-02
Author:         Luo xiaowei
Run frequency:  On demand
###

debug = DEBUG()
yargs = require 'yargs'
speed = INCLUDE 'lib.speed'
helpers = INCLUDE 'lib.helpers'
# 尝试加载llmHelper，如果失败则使用内联函数
try
  llmHelper = INCLUDE 'libapp.llmTranslationHelper'
catch error
  debug.warn "无法加载llmHelper模块，使用内联函数:", error.message
  llmHelper = null

# 内联验证函数（如果llmHelper加载失败）
validateTemplateStructure = (template) ->
  errors = []

  unless template?
    errors.push '模板对象为空'
    return {valid: false, errors}

  # 检查必要字段
  unless template._id
    errors.push '缺少_id字段'

  unless template.nm
    errors.push '缺少nm字段'

  unless template.scenario
    errors.push '缺少scenario字段'

  unless template.tpl?.main
    errors.push '缺少tpl.main字段'

  unless template.vars and Array.isArray(template.vars)
    errors.push '缺少vars字段或格式错误'

  return {
    valid: errors.length is 0
    errors: errors
  }

# 数据库集合
PromptsCol = COLLECTION 'chome', 'prompts'

# 速度监控器
speedMeter = speed.createSpeedMeter {
  intervalTriggerCount: 5,
  intervalCallback: (speedMeter) ->
    debug.info speedMeter.toString()
}

# 命令行参数解析
yargs
  .option 'category', {
    type: 'string'
    description: '指定导入的模板类别'
    default: 'all'
  }
  .option 'force', {
    type: 'boolean'
    description: '强制更新现有模板'
    default: false
  }
  .option 'dryrun', {
    type: 'boolean'
    description: '干运行模式，只显示操作不执行'
    default: false
  }
  .help('help')

options = yargs.argv

# 全局变量
dryRun = options.dryrun or AVGS.indexOf('dryrun') >= 0
forceUpdate = options.force or AVGS.indexOf('force') >= 0
category = options.category?.toLowerCase() or 'all'
startTs = new Date()

###
通用翻译模板 - 支持任意语言对翻译，多AI模型支持
###
UNIVERSAL_TRANSLATION_TEMPLATES = [
  {
    _id: "universal_translation_gpt_v1"
    nm: "通用多语言翻译模板 (GPT)"
    desc: "支持任意语言对之间的翻译，具备上下文感知能力，使用GPT模型"
    ver: 1
    status: "active"
    scenario: "universal_translation"
    tags: ["universal", "multilingual", "context_aware", "general"]
    m_cfg: {
      m_nm: "gpt"
      params: {
        temperature: 0.3
        max_tokens: 500
        top_p: 0.9
      }
    }
    tpl: {
      main: "Please translate the following text from {source_language} to {target_language}.\n\nContext: {context}\n\nRequirements:\n1. Maintain the original meaning and tone\n2. Consider the provided context for accurate translation\n3. Preserve proper nouns and technical terms when appropriate\n4. Return only the translated text without additional explanations\n\nText to translate: {text}"
      sys: "You are a professional translator with expertise in multiple languages. You provide accurate, context-aware translations while preserving the original meaning and cultural nuances."
    }
    vars: [
      {nm: "text", tp: "string", req: true, desc: "需要翻译的文本内容"}
      {nm: "source_language", tp: "string", req: true, desc: "源语言"}
      {nm: "target_language", tp: "string", req: true, desc: "目标语言"}
      {nm: "context", tp: "string", req: false, desc: "翻译上下文信息"}
    ]
    ts: new Date("2025-07-02T10:00:00.000Z")
    _mt: new Date("2025-07-02T10:00:00.000Z")
  },
  {
    _id: "universal_translation_claude_v1"
    nm: "通用多语言翻译模板 (Claude)"
    desc: "支持任意语言对之间的翻译，具备上下文感知能力，使用Claude模型"
    ver: 1
    status: "active"
    scenario: "universal_translation"
    tags: ["universal", "multilingual", "context_aware", "general"]
    m_cfg: {
      m_nm: "claude"
      params: {
        temperature: 0.3
        max_tokens: 500
        top_p: 0.9
      }
    }
    tpl: {
      main: "Please translate the following text from {source_language} to {target_language}.\n\nContext: {context}\n\nRequirements:\n1. Maintain the original meaning and tone\n2. Consider the provided context for accurate translation\n3. Preserve proper nouns and technical terms when appropriate\n4. Return only the translated text without additional explanations\n\nText to translate: {text}"
      sys: "You are a professional translator with expertise in multiple languages. You provide accurate, context-aware translations while preserving the original meaning and cultural nuances."
    }
    vars: [
      {nm: "text", tp: "string", req: true, desc: "需要翻译的文本内容"}
      {nm: "source_language", tp: "string", req: true, desc: "源语言"}
      {nm: "target_language", tp: "string", req: true, desc: "目标语言"}
      {nm: "context", tp: "string", req: false, desc: "翻译上下文信息"}
    ]
    ts: new Date("2025-07-02T10:00:00.000Z")
    _mt: new Date("2025-07-02T10:00:00.000Z")
  },
  {
    _id: "universal_translation_gemini_v1"
    nm: "通用多语言翻译模板 (Gemini)"
    desc: "支持任意语言对之间的翻译，具备上下文感知能力，使用Gemini模型"
    ver: 1
    status: "active"
    scenario: "universal_translation"
    tags: ["universal", "multilingual", "context_aware", "general"]
    m_cfg: {
      m_nm: "gemini"
      params: {
        temperature: 0.3
        max_tokens: 500
        top_p: 0.9
      }
    }
    tpl: {
      main: "Please translate the following text from {source_language} to {target_language}.\n\nContext: {context}\n\nRequirements:\n1. Maintain the original meaning and tone\n2. Consider the provided context for accurate translation\n3. Preserve proper nouns and technical terms when appropriate\n4. Return only the translated text without additional explanations\n\nText to translate: {text}"
      sys: "You are a professional translator with expertise in multiple languages. You provide accurate, context-aware translations while preserving the original meaning and cultural nuances."
    }
    vars: [
      {nm: "text", tp: "string", req: true, desc: "需要翻译的文本内容"}
      {nm: "source_language", tp: "string", req: true, desc: "源语言"}
      {nm: "target_language", tp: "string", req: true, desc: "目标语言"}
      {nm: "context", tp: "string", req: false, desc: "翻译上下文信息"}
    ]
    ts: new Date("2025-07-02T10:00:00.000Z")
    _mt: new Date("2025-07-02T10:00:00.000Z")
  },
  {
    _id: "universal_translation_deepseek_v1"
    nm: "通用多语言翻译模板 (DeepSeek)"
    desc: "支持任意语言对之间的翻译，具备上下文感知能力，使用DeepSeek模型"
    ver: 1
    status: "active"
    scenario: "universal_translation"
    tags: ["universal", "multilingual", "context_aware", "general"]
    m_cfg: {
      m_nm: "deepseek"
      params: {
        temperature: 0.3
        max_tokens: 500
        top_p: 0.9
      }
    }
    tpl: {
      main: "Please translate the following text from {source_language} to {target_language}.\n\nContext: {context}\n\nRequirements:\n1. Maintain the original meaning and tone\n2. Consider the provided context for accurate translation\n3. Preserve proper nouns and technical terms when appropriate\n4. Return only the translated text without additional explanations\n\nText to translate: {text}"
      sys: "You are a professional translator with expertise in multiple languages. You provide accurate, context-aware translations while preserving the original meaning and cultural nuances."
    }
    vars: [
      {nm: "text", tp: "string", req: true, desc: "需要翻译的文本内容"}
      {nm: "source_language", tp: "string", req: true, desc: "源语言"}
      {nm: "target_language", tp: "string", req: true, desc: "目标语言"}
      {nm: "context", tp: "string", req: false, desc: "翻译上下文信息"}
    ]
    ts: new Date("2025-07-02T10:00:00.000Z")
    _mt: new Date("2025-07-02T10:00:00.000Z")
  }
]

###
UI翻译模板 - 支持多语言对和上下文，多AI模型支持
###
UI_TRANSLATION_TEMPLATES = [
  {
    _id: "ui_translation_universal_gpt_v1"
    nm: "UI界面通用翻译模板 (GPT)"
    desc: "专用于UI界面元素的多语言翻译，支持上下文感知，使用GPT模型"
    ver: 1
    status: "active"
    scenario: "ui_translation"
    tags: ["ui", "interface", "short_text", "context_aware", "multilingual"]
    m_cfg: {
      m_nm: "gpt"
      params: {
        temperature: 0.2
        max_tokens: 200
        top_p: 0.9
      }
    }
    tpl: {
      main: "Translate the following UI interface text from {source_language} to {target_language}.\n\nContext: {context}\n\nRequirements:\n1. Keep it concise and accurate\n2. Follow target language UI conventions\n3. Maintain professional terminology accuracy\n4. Consider the interface context provided\n5. Return only the translation\n\nUI text: {text}"
      sys: "You are a professional UI/UX translator specializing in interface localization. Your translations are concise, accurate, and follow the target language's UI conventions."
    }
    vars: [
      {nm: "text", tp: "string", req: true, desc: "需要翻译的UI文本"}
      {nm: "source_language", tp: "string", req: true, desc: "源语言"}
      {nm: "target_language", tp: "string", req: true, desc: "目标语言"}
      {nm: "context", tp: "string", req: false, desc: "UI界面上下文"}
    ]
    ts: new Date("2025-07-02T10:00:00.000Z")
    _mt: new Date("2025-07-02T10:00:00.000Z")
  },
  {
    _id: "ui_translation_universal_claude_v1"
    nm: "UI界面通用翻译模板 (Claude)"
    desc: "专用于UI界面元素的多语言翻译，支持上下文感知，使用Claude模型"
    ver: 1
    status: "active"
    scenario: "ui_translation"
    tags: ["ui", "interface", "short_text", "context_aware", "multilingual"]
    m_cfg: {
      m_nm: "claude"
      params: {
        temperature: 0.2
        max_tokens: 200
        top_p: 0.9
      }
    }
    tpl: {
      main: "Translate the following UI interface text from {source_language} to {target_language}.\n\nContext: {context}\n\nRequirements:\n1. Keep it concise and accurate\n2. Follow target language UI conventions\n3. Maintain professional terminology accuracy\n4. Consider the interface context provided\n5. Return only the translation\n\nUI text: {text}"
      sys: "You are a professional UI/UX translator specializing in interface localization. Your translations are concise, accurate, and follow the target language's UI conventions."
    }
    vars: [
      {nm: "text", tp: "string", req: true, desc: "需要翻译的UI文本"}
      {nm: "source_language", tp: "string", req: true, desc: "源语言"}
      {nm: "target_language", tp: "string", req: true, desc: "目标语言"}
      {nm: "context", tp: "string", req: false, desc: "UI界面上下文"}
    ]
    ts: new Date("2025-07-02T10:00:00.000Z")
    _mt: new Date("2025-07-02T10:00:00.000Z")
  },
  {
    _id: "ui_translation_universal_gemini_v1"
    nm: "UI界面通用翻译模板 (Gemini)"
    desc: "专用于UI界面元素的多语言翻译，支持上下文感知，使用Gemini模型"
    ver: 1
    status: "active"
    scenario: "ui_translation"
    tags: ["ui", "interface", "short_text", "context_aware", "multilingual"]
    m_cfg: {
      m_nm: "gemini"
      params: {
        temperature: 0.2
        max_tokens: 200
        top_p: 0.9
      }
    }
    tpl: {
      main: "Translate the following UI interface text from {source_language} to {target_language}.\n\nContext: {context}\n\nRequirements:\n1. Keep it concise and accurate\n2. Follow target language UI conventions\n3. Maintain professional terminology accuracy\n4. Consider the interface context provided\n5. Return only the translation\n\nUI text: {text}"
      sys: "You are a professional UI/UX translator specializing in interface localization. Your translations are concise, accurate, and follow the target language's UI conventions."
    }
    vars: [
      {nm: "text", tp: "string", req: true, desc: "需要翻译的UI文本"}
      {nm: "source_language", tp: "string", req: true, desc: "源语言"}
      {nm: "target_language", tp: "string", req: true, desc: "目标语言"}
      {nm: "context", tp: "string", req: false, desc: "UI界面上下文"}
    ]
    ts: new Date("2025-07-02T10:00:00.000Z")
    _mt: new Date("2025-07-02T10:00:00.000Z")
  },
  {
    _id: "ui_translation_universal_deepseek_v1"
    nm: "UI界面通用翻译模板 (DeepSeek)"
    desc: "专用于UI界面元素的多语言翻译，支持上下文感知，使用DeepSeek模型"
    ver: 1
    status: "active"
    scenario: "ui_translation"
    tags: ["ui", "interface", "short_text", "context_aware", "multilingual"]
    m_cfg: {
      m_nm: "deepseek"
      params: {
        temperature: 0.2
        max_tokens: 200
        top_p: 0.9
      }
    }
    tpl: {
      main: "Translate the following UI interface text from {source_language} to {target_language}.\n\nContext: {context}\n\nRequirements:\n1. Keep it concise and accurate\n2. Follow target language UI conventions\n3. Maintain professional terminology accuracy\n4. Consider the interface context provided\n5. Return only the translation\n\nUI text: {text}"
      sys: "You are a professional UI/UX translator specializing in interface localization. Your translations are concise, accurate, and follow the target language's UI conventions."
    }
    vars: [
      {nm: "text", tp: "string", req: true, desc: "需要翻译的UI文本"}
      {nm: "source_language", tp: "string", req: true, desc: "源语言"}
      {nm: "target_language", tp: "string", req: true, desc: "目标语言"}
      {nm: "context", tp: "string", req: false, desc: "UI界面上下文"}
    ]
    ts: new Date("2025-07-02T10:00:00.000Z")
    _mt: new Date("2025-07-02T10:00:00.000Z")
  }
]

###
论坛翻译模板 - 支持多语言对和上下文，多AI模型支持
###
FORUM_TRANSLATION_TEMPLATES = [
  {
    _id: "forum_translation_universal_gpt_v1"
    nm: "论坛内容通用翻译模板 (GPT)"
    desc: "专用于论坛帖子、评论等动态内容的多语言翻译，使用GPT模型"
    ver: 1
    status: "active"
    scenario: "forum_translation"
    tags: ["forum", "social", "long_text", "natural_language", "multilingual"]
    m_cfg: {
      m_nm: "gpt"
      params: {
        temperature: 0.3
        max_tokens: 600
        top_p: 0.9
      }
    }
    tpl: {
      main: "Translate the following forum content from {source_language} to {target_language}.\n\nContext: {context}\n\nRequirements:\n1. Maintain natural and fluent expression\n2. Preserve the original tone and emotion\n3. Apply appropriate localization\n4. Keep proper nouns and place names unchanged\n5. Consider the forum discussion context\n6. For HTML content: Only translate text within HTML tags, preserve all HTML tags, attributes, and structure unchanged\n   - Example: '<p class=\"content\">这是内容</p>' → '<p class=\"content\">[translated text]</p>'\n   - Keep all HTML tags like <p>, <div>, <span>, <a>, <img>, etc. exactly as they are\n   - Preserve all attributes like class, id, style, href, src, etc.\n7. Return only the translation\n\nForum content: {text}"
      sys: "You are a professional content translator specializing in social media and forum content. You excel at translating forum posts into natural, fluent language while preserving the original tone and emotion. When handling HTML content, you only translate the text within HTML tags while keeping all HTML structure, tags, and attributes completely unchanged."
    }
    vars: [
      {nm: "text", tp: "string", req: true, desc: "需要翻译的论坛内容"}
      {nm: "source_language", tp: "string", req: true, desc: "源语言"}
      {nm: "target_language", tp: "string", req: true, desc: "目标语言"}
      {nm: "context", tp: "string", req: false, desc: "论坛讨论上下文"}
    ]
    ts: new Date("2025-07-02T10:00:00.000Z")
    _mt: new Date("2025-07-02T10:00:00.000Z")
  },
  {
    _id: "forum_translation_universal_claude_v1"
    nm: "论坛内容通用翻译模板 (Claude)"
    desc: "专用于论坛帖子、评论等动态内容的多语言翻译，使用Claude模型"
    ver: 1
    status: "active"
    scenario: "forum_translation"
    tags: ["forum", "social", "long_text", "natural_language", "multilingual"]
    m_cfg: {
      m_nm: "claude"
      params: {
        temperature: 0.3
        max_tokens: 600
        top_p: 0.9
      }
    }
    tpl: {
      main: "Translate the following forum content from {source_language} to {target_language}.\n\nContext: {context}\n\nRequirements:\n1. Maintain natural and fluent expression\n2. Preserve the original tone and emotion\n3. Apply appropriate localization\n4. Keep proper nouns and place names unchanged\n5. Consider the forum discussion context\n6. For HTML content: Only translate text within HTML tags, preserve all HTML tags, attributes, and structure unchanged\n   - Example: '<p class=\"content\">这是内容</p>' → '<p class=\"content\">[translated text]</p>'\n   - Keep all HTML tags like <p>, <div>, <span>, <a>, <img>, etc. exactly as they are\n   - Preserve all attributes like class, id, style, href, src, etc.\n7. Return only the translation\n\nForum content: {text}"
      sys: "You are a professional content translator specializing in social media and forum content. You excel at translating forum posts into natural, fluent language while preserving the original tone and emotion. When handling HTML content, you only translate the text within HTML tags while keeping all HTML structure, tags, and attributes completely unchanged."
    }
    vars: [
      {nm: "text", tp: "string", req: true, desc: "需要翻译的论坛内容"}
      {nm: "source_language", tp: "string", req: true, desc: "源语言"}
      {nm: "target_language", tp: "string", req: true, desc: "目标语言"}
      {nm: "context", tp: "string", req: false, desc: "论坛讨论上下文"}
    ]
    ts: new Date("2025-07-02T10:00:00.000Z")
    _mt: new Date("2025-07-02T10:00:00.000Z")
  }
]

###
评论翻译模板 - 专用于用户评论内容翻译
###
COMMENT_TRANSLATION_TEMPLATES = [
  {
    _id: "comment_translation_universal_gpt_v1"
    nm: "用户评论通用翻译模板"
    desc: "专用于用户评论、回复等短文本动态内容的多语言翻译"
    ver: 1
    status: "active"
    scenario: "comment_translation"
    tags: ["comment", "social", "short_text", "casual", "multilingual"]
    m_cfg: {
      m_nm: "gpt"
      params: {
        temperature: 0.2
        max_tokens: 300
        top_p: 0.9
      }
    }
    tpl: {
      main: "Translate the following user comment from {source_language} to {target_language}.\n\nContext: {context}\n\nRequirements:\n1. Maintain casual and natural expression typical of user comments\n2. Preserve the original tone, emotion, and informal language style\n3. Keep internet slang, abbreviations, and emoji unchanged when appropriate\n4. Apply appropriate localization for cultural references\n5. Keep proper nouns and place names unchanged\n6. For HTML content: Only translate text within HTML tags, preserve all HTML tags, attributes, and structure unchanged\n   - Example: '<span class=\"highlight\">很好</span>' → '<span class=\"highlight\">[translated text]</span>'\n   - Keep all HTML tags like <p>, <div>, <span>, <a>, <strong>, <em>, etc. exactly as they are\n   - Preserve all attributes like class, id, style, href, etc.\n7. Return only the translation\n\nUser comment: {text}"
      sys: "You are a professional translator specializing in social media and user-generated content. You excel at translating user comments while preserving their casual, authentic tone and informal language style. When handling HTML content, you only translate the text within HTML tags while keeping all HTML structure, tags, and attributes completely unchanged."
    }
    vars: [
      {nm: "text", tp: "string", req: true, desc: "需要翻译的用户评论内容"}
      {nm: "source_language", tp: "string", req: true, desc: "源语言"}
      {nm: "target_language", tp: "string", req: true, desc: "目标语言"}
      {nm: "context", tp: "string", req: false, desc: "评论上下文信息"}
    ]
    ts: new Date("2025-07-02T10:00:00.000Z")
    _mt: new Date("2025-07-02T10:00:00.000Z")
  }
]

###
房产翻译模板 - 专业房产内容翻译
###
PROPERTY_TRANSLATION_TEMPLATES = [
  {
    _id: "property_translation_universal_gpt_v1"
    nm: "房产内容通用翻译模板"
    desc: "专用于房产描述、房源信息等专业内容的多语言翻译"
    ver: 1
    status: "active"
    scenario: "property_translation"
    tags: ["property", "real_estate", "professional", "canada", "multilingual"]
    m_cfg: {
      m_nm: "gpt"
      params: {
        temperature: 0.2
        max_tokens: 700
        top_p: 0.9
      }
    }
    tpl: {
      main: "Translate the following Canadian real estate content from {source_language} to {target_language}.\n\nContext: {context}\n\nRequirements:\n1. Maintain professional terminology accuracy\n2. Do NOT translate place names, street names, or geographic locations\n3. Preserve the attractiveness of property descriptions\n4. Follow target language real estate description conventions\n5. Consider the Canadian real estate context\n6. Return only the translation\n\nReal estate content: {text}"
      sys: "You are a professional real estate translator with expertise in Canadian real estate terminology. You provide accurate translations of property descriptions while maintaining professionalism and appeal."
    }
    vars: [
      {nm: "text", tp: "string", req: true, desc: "需要翻译的房产内容"}
      {nm: "source_language", tp: "string", req: true, desc: "源语言"}
      {nm: "target_language", tp: "string", req: true, desc: "目标语言"}
      {nm: "context", tp: "string", req: false, desc: "房产相关上下文"}
    ]
    ts: new Date("2025-07-02T10:00:00.000Z")
    _mt: new Date("2025-07-02T10:00:00.000Z")
  }
]

###
评论过滤模板 - 多语言内容审核
###
COMMENT_FILTER_TEMPLATES = [
  {
    _id: "comment_filter_universal_gpt_v1"
    nm: "通用评论内容过滤模板"
    desc: "用于多语言评论内容过滤，检测不当内容"
    ver: 1
    status: "active"
    scenario: "comment_filter"
    tags: ["comment", "filter", "moderation", "multilingual", "content_safety"]
    m_cfg: {
      m_nm: "gpt"
      params: {
        temperature: 0.1
        max_tokens: 150
        top_p: 0.8
      }
    }
    tpl: {
      main: "Analyze the following content for appropriateness.\n\nContext: {context}\n\nCheck for: spam, advertisements, political content, terrorism, abuse, pornography, meaningless content, or other inappropriate material.\n\nRespond with either 'PASS' if the content is appropriate, or 'REJECT: [specific reason]' if it should be blocked.\n\nContent to analyze: {text}"
      sys: "You are a professional content moderator with expertise in identifying inappropriate content across multiple languages. You make fair and accurate judgments based on community guidelines and content policies."
    }
    vars: [
      {nm: "text", tp: "string", req: true, desc: "需要过滤检查的内容"}
      {nm: "context", tp: "string", req: false, desc: "内容发布的上下文环境"}
    ]
    ts: new Date("2025-07-02T10:00:00.000Z")
    _mt: new Date("2025-07-02T10:00:00.000Z")
  }
]

# 所有模板数据汇总
ALL_TEMPLATES = [].concat(
  UNIVERSAL_TRANSLATION_TEMPLATES,
  UI_TRANSLATION_TEMPLATES,
  FORUM_TRANSLATION_TEMPLATES,
  COMMENT_TRANSLATION_TEMPLATES,
  PROPERTY_TRANSLATION_TEMPLATES,
  COMMENT_FILTER_TEMPLATES
)

# 类别映射
CATEGORY_MAPPING = {
  'universal': UNIVERSAL_TRANSLATION_TEMPLATES
  'ui': UI_TRANSLATION_TEMPLATES
  'forum': FORUM_TRANSLATION_TEMPLATES
  'comment_translation': COMMENT_TRANSLATION_TEMPLATES
  'property': PROPERTY_TRANSLATION_TEMPLATES
  'comment_filter': COMMENT_FILTER_TEMPLATES
  'all': ALL_TEMPLATES
}

###
为现有模板生成多AI模型版本
@param {Object} baseTemplate - 基础模板
@param {String} modelName - AI模型名称
@return {Object} 新的模板对象
###
generateModelVariant = (baseTemplate, modelName) ->
  # 模型名称映射
  modelDisplayNames = {
    'gpt': 'GPT'
    'claude': 'Claude'
    'gemini': 'Gemini'
    'deepseek': 'DeepSeek'
    'grok': 'Grok'
  }

  # 创建新模板
  newTemplate = JSON.parse(JSON.stringify(baseTemplate))

  # 更新ID和名称
  originalId = baseTemplate._id
  newTemplate._id = originalId.replace(/_gpt_/, "_#{modelName}_")
  newTemplate.nm = baseTemplate.nm.replace('(GPT)', "(#{modelDisplayNames[modelName] or modelName})")
  newTemplate.desc = baseTemplate.desc.replace('使用GPT模型', "使用#{modelDisplayNames[modelName] or modelName}模型")

  # 更新模型配置
  newTemplate.m_cfg.m_nm = modelName

  return newTemplate

###
生成所有模板的多AI模型版本
###
generateAllModelVariants = ->
  allTemplates = []
  supportedModels = ['gpt', 'claude', 'gemini', 'deepseek']

  # 为每个基础模板生成多个模型版本
  baseTemplates = [
    UNIVERSAL_TRANSLATION_TEMPLATES[0],  # 只取第一个作为基础
    UI_TRANSLATION_TEMPLATES[0],
    FORUM_TRANSLATION_TEMPLATES[0]
  ]

  for baseTemplate in baseTemplates
    for modelName in supportedModels
      if modelName is 'gpt'
        # GPT版本已存在，直接添加
        allTemplates.push baseTemplate
      else
        # 生成其他模型版本
        variant = generateModelVariant(baseTemplate, modelName)
        allTemplates.push variant

  return allTemplates

# 重新生成所有模板
ALL_GENERATED_TEMPLATES = generateAllModelVariants()

# 更新类别映射以使用生成的模板
CATEGORY_MAPPING['generated'] = ALL_GENERATED_TEMPLATES

###
获取要导入的模板数据
@param {String} category - 模板类别
@return {Array} 模板数据数组
###
getTemplatesToImport = (category) ->
  # 如果请求所有模板，使用生成的多AI模型版本
  if category is 'all'
    return ALL_GENERATED_TEMPLATES

  templates = CATEGORY_MAPPING[category]
  unless templates and Array.isArray(templates)
    debug.warn "不支持的模板类别: #{category}，支持的类别: #{Object.keys(CATEGORY_MAPPING).join(', ')}"
    return []

  return templates

###
检查模板是否已存在
@param {String} templateId - 模板ID
@return {Object} 现有模板数据或null
###
checkTemplateExists = (templateId) ->
  try
    return await PromptsCol.findOne {_id: templateId}
  catch error
    debug.error "检查模板存在性失败:", error
    return null

###
导入单个模板
@param {Object} template - 模板数据
@param {Boolean} forceUpdate - 是否强制更新
@return {Object} 导入结果
###
importTemplate = (template, forceUpdate) ->
  # 验证模板数据
  if llmHelper?.validateTemplateStructure
    validation = llmHelper.validateTemplateStructure(template)
  else
    validation = validateTemplateStructure(template)

  unless validation.valid
    return {
      success: false
      error: "模板验证失败: #{validation.errors.join('; ')}"
      templateId: template._id
    }

  # 检查是否已存在
  existing = await checkTemplateExists(template._id)

  if existing and not forceUpdate
    return {
      success: false
      error: "模板已存在，使用 --force 参数强制更新"
      templateId: template._id
      skipped: true
    }

  # 执行导入或更新
  try
    if existing and forceUpdate
      # 更新现有模板
      result = await PromptsCol.replaceOne({_id: template._id}, template)
      if result.modifiedCount > 0
        return {
          success: true
          action: 'updated'
          templateId: template._id
          modifiedCount: result.modifiedCount
        }
      else
        return {
          success: false
          error: '更新失败'
          templateId: template._id
        }
    else
      # 创建新模板
      result = await PromptsCol.insertOne(template)
      if result.insertedId
        return {
          success: true
          action: 'created'
          templateId: template._id
          insertedId: result.insertedId
        }
      else
        return {
          success: false
          error: '插入失败'
          templateId: template._id
        }
  catch error
    debug.error "导入模板异常 #{template._id}:", error
    return {
      success: false
      error: error.message
      templateId: template._id
    }

###
生成导入报告
@param {Object} stats - 统计数据
###
generateReport = (stats) ->
  report = []
  report.push '=== LLM翻译模板导入报告 ==='
  report.push "导入时间: #{new Date().toISOString()}"
  report.push "导入类别: #{category}"
  report.push "导入模式: #{if dryRun then '干运行' else '实际执行'}"
  report.push "强制更新: #{if forceUpdate then '是' else '否'}"
  report.push ''

  report.push '=== 导入统计 ==='
  report.push "总处理模板: #{stats.processed or 0}"
  report.push "成功创建: #{stats.created or 0}"
  report.push "成功更新: #{stats.updated or 0}"
  report.push "跳过模板: #{stats.skipped or 0}"
  report.push "导入错误: #{stats.errors or 0}"
  report.push "验证错误: #{stats.validationErrors or 0}"

  if stats.processed > 0
    successCount = (stats.created or 0) + (stats.updated or 0)
    successRate = ((successCount / stats.processed) * 100).toFixed(2)
    report.push "导入成功率: #{successRate}%"

  if stats.errorDetails and stats.errorDetails.length > 0
    report.push ''
    report.push '=== 错误详情 ==='
    for error in stats.errorDetails.slice(0, 10)  # 只显示前10个错误
      report.push "- #{error.templateId}: #{error.error}"

  report.push ''
  report.push '=== 建议 ==='
  if (stats.errors or 0) > 0
    report.push '- 存在导入错误，建议检查模板数据格式和数据库连接'
  if (stats.skipped or 0) > 0 and not forceUpdate
    report.push '- 有模板被跳过，如需更新现有模板请使用 --force 参数'
  if successCount > 0
    report.push '- 导入成功，可以在批量翻译脚本中使用这些模板'

  debug.info report.join('\n')

###
执行模板导入
###
executeImport = ->
  # 获取要导入的模板
  templates = getTemplatesToImport(category)

  if templates.length is 0
    debug.warn "没有找到要导入的模板数据"
    return EXIT 0

  debug.info "准备导入 #{templates.length} 个模板，类别: #{category}"
  debug.info "干运行模式: #{dryRun}"
  debug.info "强制更新: #{forceUpdate}"

  # 统计数据
  stats = {
    processed: 0
    created: 0
    updated: 0
    skipped: 0
    errors: 0
    validationErrors: 0
    errorDetails: []
  }

  # 处理每个模板
  for template in templates
    speedMeter.check { processed: 1 }
    stats.processed++

    debug.debug "处理模板: #{template._id} - #{template.nm}"

    if dryRun
      # 干运行模式，只验证不导入
      if llmHelper?.validateTemplateStructure
        validation = llmHelper.validateTemplateStructure(template)
      else
        validation = validateTemplateStructure(template)
      if validation.valid
        existing = await checkTemplateExists(template._id)
        action = if existing then (if forceUpdate then 'update' else 'skip') else 'create'
        debug.info "DryRun: 模板 #{template._id} 将被 #{action}"
        speedMeter.check { dryRun: 1 }
      else
        debug.warn "DryRun: 模板 #{template._id} 验证失败: #{validation.errors.join('; ')}"
        stats.validationErrors++
        speedMeter.check { validationError: 1 }
    else
      # 实际导入
      try
        result = await importTemplate(template, forceUpdate)

        if result.success
          if result.action is 'created'
            stats.created++
            speedMeter.check { created: 1 }
            debug.info "成功创建模板: #{result.templateId}"
          else if result.action is 'updated'
            stats.updated++
            speedMeter.check { updated: 1 }
            debug.info "成功更新模板: #{result.templateId}"
        else
          if result.skipped
            stats.skipped++
            speedMeter.check { skipped: 1 }
            debug.debug "跳过模板: #{result.templateId} - #{result.error}"
          else
            stats.errors++
            stats.errorDetails.push({
              templateId: result.templateId
              error: result.error
            })
            speedMeter.check { error: 1 }
            debug.error "导入模板失败: #{result.templateId} - #{result.error}"
      catch error
        stats.errors++
        stats.errorDetails.push({
          templateId: template._id
          error: error.message
        })
        speedMeter.check { error: 1 }
        debug.error "导入模板异常: #{template._id}", error

  # 生成报告
  processTs = (new Date().getTime() - startTs.getTime()) / (1000 * 60)
  stats.processTime = processTs
  generateReport(stats)

  debug.info "导入完成，总处理时间 #{processTs} 分钟，#{speedMeter.toString()}"
  EXIT 0

###
主执行函数
###
main = ->
  debug.info '开始LLM翻译模板导入...'

  try
    await executeImport()
  catch error
    debug.error '导入过程中出现错误:', error
    EXIT 1

# 启动主函数
main()
