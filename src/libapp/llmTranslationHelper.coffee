###
LLM翻译系统公共模块
提供上下文处理、语言映射等共享功能

<AUTHOR>
@date 2025-07-02
###

# 上下文标识符映射 - 基于I18N_CTX常量
CONTEXT_MAPPING = {
  'menu': '菜单界面'
  'prop': '加拿大房产信息'
  'evaluation': '房产评估'
  'showing': '看房预约'
  'cpm': '广告管理'
  'city': '城市信息'
  'yellowpage': '黄页商家'
  'school': '学校信息'
  'tools': '工具功能'
  'forum': '论坛讨论'
  'form': '表单填写'
  'project': '楼盘项目'
  'state': '状态信息'
  'location': '地理位置'
  'month': '月份时间'
  'unit': '单位换算'
  'time': '时间信息'
  'census': '人口统计'
}

# 语言映射
LANGUAGE_MAPPING = {
  'en': 'English'
  'zh-cn': 'Chinese Simplified'
  'zh': 'Chinese Traditional'
  'kr': 'Korean'
}

# 语言对应的本地化名称
LANGUAGE_NATIVE_NAMES = {
  'en': 'English'
  'zh-cn': '中文简体'
  'zh': '中文繁體'
  'kr': '한국어'
}

###
从_id中提取上下文标识符
@param {String} id - i18n记录的_id
@return {Object} {cleanText: string, context: string, contextDesc: string}
###
extractContextFromId = (id) ->
  unless id and typeof id is 'string'
    return {cleanText: id, context: '', contextDesc: ''}
  
  # 查找最后一个冒号后的内容作为上下文标识符
  lastColonIndex = id.lastIndexOf(':')
  if lastColonIndex > 0 and lastColonIndex < id.length - 1
    cleanText = id.substring(0, lastColonIndex)
    contextId = id.substring(lastColonIndex + 1)
    contextDesc = CONTEXT_MAPPING[contextId] or ''
    
    return {
      cleanText: cleanText
      context: contextId
      contextDesc: contextDesc
    }
  
  return {cleanText: id, context: '', contextDesc: ''}

###
获取默认上下文描述
@param {String} text - 文本内容
@param {String} extractedContext - 从_id提取的上下文
@return {String} 上下文描述
###
getDefaultContext = (text, extractedContext) ->
  # 如果已有提取的上下文，优先使用
  if extractedContext
    return CONTEXT_MAPPING[extractedContext] or extractedContext
  
  # 对于短文本（≤50字符且无空格），默认使用房产上下文
  if text and text.length <= 50 and not /\s/.test(text.trim())
    return CONTEXT_MAPPING['prop'] or '加拿大房产信息'
  
  return ''

###
智能选择翻译场景
@param {String} text - 文本内容
@param {String} context - 上下文信息
@return {String} 场景类型
###
selectTranslationScenario = (text, context) ->
  # 如果有评论相关上下文，使用评论翻译模板
  if context and (context.includes('comment') or context.includes('评论') or
                  context.includes('cmnt'))
    return 'comment_translation'

  # 如果有论坛相关上下文，使用论坛翻译模板
  if context and (context.includes('forum') or context.includes('论坛') or
                  context.includes('post'))
    return 'forum_translation'

  # 如果有房产相关上下文，使用房产翻译模板
  if context and (context.includes('房产') or context.includes('real estate') or
                  context.includes('prop'))
    return 'property_translation'

  # 短文本使用UI翻译模板
  if text and text.length <= 50 and not /\s/.test(text.trim())
    return 'ui_translation'

  # 长文本使用论坛翻译模板
  if text and text.length > 200
    return 'forum_translation'

  # 默认使用通用翻译模板
  return 'universal_translation'

###
简单的变量替换函数
@param {String} template - 模板字符串
@param {Object} variables - 变量对象
@return {String} 替换后的字符串
###
replaceTemplateVariables = (template, variables) ->
  unless template and typeof template is 'string'
    return template
  
  result = template
  for key, value of variables
    if value?
      regex = new RegExp("\\{#{key}\\}", 'g')
      result = result.replace(regex, String(value))
  
  return result

###
验证翻译模板结构
@param {Object} template - 模板对象
@return {Object} {valid: boolean, errors: string[]}
###
validateTemplateStructure = (template) ->
  errors = []
  
  unless template?
    errors.push '模板对象为空'
    return {valid: false, errors}
  
  # 检查必要字段
  unless template._id
    errors.push '缺少_id字段'
  
  unless template.nm
    errors.push '缺少nm字段'
  
  unless template.scenario
    errors.push '缺少scenario字段'
  
  unless template.tpl?.main
    errors.push '缺少tpl.main字段'
  
  unless template.vars and Array.isArray(template.vars)
    errors.push '缺少vars字段或格式错误'
  
  # 检查变量定义
  if template.vars
    for variable in template.vars
      unless variable.nm
        errors.push "变量缺少nm字段: #{JSON.stringify(variable)}"
  
  return {
    valid: errors.length is 0
    errors: errors
  }

###
生成翻译统计报告
@param {Object} stats - 统计数据
@return {String} 格式化的报告
###
generateTranslationReport = (stats) ->
  report = []
  report.push '=== LLM翻译处理报告 ==='
  report.push "处理时间: #{new Date().toISOString()}"
  report.push ''

  report.push '=== 处理统计 ==='
  report.push "总处理记录: #{stats.processed or 0}"
  report.push "翻译成功: #{stats.success or 0}"
  report.push "翻译失败: #{stats.failed or 0}"
  report.push "跳过记录: #{stats.skipped or 0}"

  if stats.processed > 0
    successRate = ((stats.success or 0) / stats.processed * 100).toFixed(2)
    report.push "成功率: #{successRate}%"

  if stats.contextStats
    report.push ''
    report.push '=== 上下文统计 ==='
    for context, count of stats.contextStats
      report.push "#{context}: #{count} 次"

  if stats.languageStats
    report.push ''
    report.push '=== 语言统计 ==='
    for lang, count of stats.languageStats
      langName = LANGUAGE_NATIVE_NAMES[lang] or lang
      report.push "#{langName}: #{count} 次"

  if stats.errorDetails and stats.errorDetails.length > 0
    report.push ''
    report.push '=== 错误详情 ==='
    for error in stats.errorDetails.slice(0, 10)  # 只显示前10个错误
      report.push "- #{error.id}: #{error.error}"

  return report.join('\n')

# 导出所有函数和常量
module.exports = {
  CONTEXT_MAPPING
  LANGUAGE_MAPPING
  LANGUAGE_NATIVE_NAMES
  extractContextFromId
  getDefaultContext
  selectTranslationScenario
  replaceTemplateVariables
  validateTemplateStructure
  generateTranslationReport
}
